# UI Flow and UX Design for Research Manager App

This document outlines the proposed UI flow and user experience (UX) strategy for the Research Manager application, based on the application requirements and the established data models, particularly the hierarchical research questions.

## 1. Overall Application Flow

```mermaid
graph TD
    A[App Launch] --> B{Project Selected?}
    B -- No --> C[Project Selection Page]
    B -- Yes --> D[Main Dashboard]
    C --> D
    D -- Manage Papers --> E[Papers Section]
    D -- Manage Research Questions --> F[Research Questions Section]
    D -- Manage Relationships --> G[Relationships Section]
    E -- Add/Edit Paper --> H[Paper Form/Modal]
    F -- Add/Edit Question/Answer --> I[Question/Answer Form/Modal]
    G -- Add/Edit Relationship --> J[Relationship Form/Modal]
    H --> E
    I --> F
    J --> G
```

## 2. Key Screens and Components

### 2.1. Project Selection Page
*   **Purpose**: First screen users see to choose or create a project.
*   **Layout**:
    *   Clear heading: "Welcome to Research Manager" or "Select a Project".
    *   Two main actions: "Create New Project" (button) and "Select Existing Project" (list/dropdown).
    *   List of existing projects, each with a "Select" button and a "Delete" button (with confirmation).
    *   A simple form (possibly in a modal) for "Create New Project" (just a project name input).

### 2.2. Main Dashboard (Project View)
*   **Purpose**: Central hub for all project-specific data.
*   **Layout**:
    *   **Header**: Displays the currently selected project name.
    *   **Sidebar Navigation**: On the left, a persistent navigation menu with links to:
        *   "Papers"
        *   "Research Questions"
        *   "Relationships"
        *   (Optional: "Project Settings", "Dashboard Overview")
    *   **Main Content Area**: The primary area on the right, which dynamically updates based on the selected sidebar item.

### 2.3. Content Sections within the Dashboard

#### a. Papers Section
*   **Display**: A table or list view of all papers belonging to the *selected project*.
    *   Columns: Title, Authors, Publication Year, Link, Notes.
    *   Actions per row: "View Details" (opens modal/new page), "Edit" (opens form), "Delete" (with confirmation).
*   **Actions**: Prominent "Add New Paper" button.
*   **Input**: A form (modal or dedicated page) for adding/editing paper details.

#### b. Research Questions Section
*   **Display**: A hierarchical, tree-like view of "Big Questions" and nested "Research Questions".
    *   Each node (Big Question or Research Question) can be expanded/collapsed to reveal children.
    *   Answers are displayed directly under their respective Research Questions when expanded.
    *   Each question node will have actions: "Add Sub-Question" (for Research Questions), "Add Answer", "Edit", "Delete" (with confirmation).
*   **Actions**: Prominent "Add Big Question" button.
*   **Input**: Forms (modals or dedicated pages) for:
    *   Adding a Big Question (text input).
    *   Adding a Research Question (text input, optional parent selection - Big Question or another Research Question).
    *   Adding an Answer (text input, paper selection, location input).

#### c. Relationships Section
*   **Display**: A list or table of all defined relationships between papers.
    *   Each entry clearly shows: `[Paper A Title] -- [Relationship Type] --> [Paper B Title]` and any notes.
    *   Actions per row: "Edit", "Delete" (with confirmation).
*   **Actions**: Prominent "Define New Relationship" button.
*   **Input**: A form (modal or dedicated page) for defining relationships:
    *   Dropdowns to select Paper A and Paper B (from the current project's papers).
    *   Dropdown/radio buttons for `RelationshipType` enum.
    *   Optional notes text area.

### 3. User Experience (UX) Considerations

*   **Form-based Input**: All data entry will use clear, accessible forms, leveraging Shadcn UI components for consistency and validation.
*   **Confirmation Prompts**: Critical deletion actions (Project, Paper, Question, Relationship) will trigger a confirmation dialog to prevent accidental data loss.
*   **Feedback Messages**: Use toast notifications or inline alerts (Shadcn UI) for success messages ("Paper added successfully!"), error messages ("Title is required."), and loading states.
*   **Responsiveness**: The layout should adapt well to different screen sizes (mobile, tablet, desktop).
*   **Intuitive Navigation**: Clear and consistent navigation elements.
