## Application Requirements Document

### 1. Goal of the Application

The primary goal of this application is to serve as a literature review and research management tool. It aims to help users organize, analyze, and synthesize information from various research papers by linking them to specific research questions and identifying relationships between them, all within the context of a defined research project. This facilitates a structured approach to understanding a body of literature and extracting key insights for specific research topics.

### 2. Core Features

#### 2.1. Project Management
- **Create Projects:** Users must be able to create new research projects, each associated with a specific research topic.
- **Select Projects:** Users must be able to select an existing project to work within. All subsequent research organization (papers, questions, answers, relationships) will be scoped to the selected project.
- **View Projects:** A list of all created projects should be accessible.
- **Delete Projects:** Users must be able to delete projects. Deleting a project should cascade and remove all associated papers, research questions, big questions, answers, and relationships within that project.

#### 2.2. Paper Management
- **Add Papers:** Users must be able to add new research papers to the *currently selected project*, providing details such as:
    - Title (required)
    - Authors
    - Publication Year
    - Link to the paper (URL or local file path)
    - General notes about the paper
- **View Papers:** All papers belonging to the *currently selected project* should be displayed in a clear, organized manner, showing their key details.
- **Delete Papers:** Users must be able to remove papers from the *currently selected project*. Deleting a paper should also remove all associated answers and relationships within that project.

#### 2.3. Research Question Management
- **Add Big Questions:** Users must be able to define overarching "Big Questions" to categorize and group related research inquiries *within the currently selected project*.
- **Add Research Questions:** Users must be able to add specific research questions *within the currently selected project*. These questions can optionally be linked to a "Big Question" or another existing "Research Question" as a parent, allowing for a hierarchical structure.
- **View Questions:** All "Big Questions" and their nested research questions *for the currently selected project* should be displayed in a clear, hierarchical, tree-like manner. Top-level research questions (without a parent) should also be displayed.
- **Delete Questions:** Users must be able to delete "Big Questions" and individual research questions *from the currently selected project*. Deleting a "Big Question" should unlink its direct child research questions. Deleting a parent "Research Question" should unlink its direct child research questions. Deleting any research question should also remove all its associated answers.

#### 2.4. Answering Research Questions
- **Add Answers:** For each research question *within the currently selected project*, users must be able to record answers found within specific papers. An answer must include:
    - The answer text
    - A link to the source paper (which must also belong to the *currently selected project*)
    - The specific location within the paper (e.g., page number, section)
- **View Answers:** Answers should be displayed directly under their respective research questions, along with their source paper and location.
- **Delete Answers:** Users must be able to delete individual answers.

#### 2.5. Paper Relationship Management
- **Define Relationships:** Users must be able to define relationships between any two papers *within the currently selected project*. Supported relationship types include:
    - Supports
    - Contradicts
    - Complements
    - Extends
    - Is extended by
    - Uses methodology of
- **Add Relationship Notes:** Users can add optional notes to describe the nature of the relationship.
- **View Relationships:** All defined relationships *for the currently selected project* should be displayed, clearly indicating the two papers involved and the type of relationship.
- **Delete Relationships:** Users must be able to remove existing relationships between papers.

### 3. User Interface & Experience
- **Project Selection:** Upon launching the application, users should be prompted to create a new project or select an existing one.
- **Dashboard View:** Once a project is selected, the application should provide a single, intuitive dashboard that displays all papers, research questions (including nested ones), big questions, and relationships *relevant to the selected project*.
- **Form-based Input:** All data entry (adding papers, questions, answers, relationships) should be handled via clear and accessible forms.
- **Confirmation Prompts:** Critical deletion actions (e.g., deleting a paper, question, project) should include confirmation prompts to prevent accidental data loss.
- **Feedback Messages:** The application should provide clear success or error messages to the user after actions are performed (e.g., "Paper added successfully!", "Title is required.").

### 4. Data Structure and Scalability Considerations
- **Robust Data Model:** The underlying data structure must be robust and well-designed to efficiently store and retrieve all entities (projects, papers, research questions, big questions, answers, relationships) and their interconnections. It should support the hierarchical nature of questions and the many-to-many relationships between papers.
- **Future Multi-User Support:** While initially designed for a single user, the data model and application architecture should be designed with future multi-user capabilities in mind. This implies that the data structure should be extensible to include user authentication and authorization, allowing multiple researchers to collaborate on the same research topic or project in the future. This might involve associating projects and their contents with specific users or groups.

### 5. Data Persistence
- All entered data (projects, papers, questions, answers, relationships) must be persistently stored, so it remains available across application sessions.

### 6. Validation
- Input fields should have basic validation (e.g., required fields, preventing a paper from being related to itself).
